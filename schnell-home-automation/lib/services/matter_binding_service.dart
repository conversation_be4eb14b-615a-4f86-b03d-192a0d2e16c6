import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// Service for Matter device binding using native CHIP-Tool SDK integration
///
/// This service provides real Matter device binding functionality by interfacing
/// with the native Android MatterBindingService that uses the CHIP-Tool SDK.
class MatterBindingService {
  static const MethodChannel _channel = MethodChannel('com.schnell.homeautomation/matter_binding');

  static MatterBindingService? _instance;
  static MatterBindingService get instance => _instance ??= MatterBindingService._();

  MatterBindingService._();

  /// Commission a device to CHIP-Tool controller using pairing code with auto-assigned node ID
  /// Node IDs start from 10 and auto-increment
  /// Equivalent to: chip-tool pairing code <auto-node-id> <setup-code>
  Future<MatterCommissioningResult> commissionDevice({
    required String setupCode,
  }) async {
    try {
      developer.log(
        'Commissioning device to CHIP-Tool controller with setup code: $setupCode (auto-assigned node ID)',
        name: 'MatterBindingService',
      );

      final Map<String, dynamic> result = await _channel.invokeMethod('commissionDevice', {
        'setupCode': setupCode,
      });

      return MatterCommissioningResult.fromMap(result);
    } catch (e) {
      developer.log('Failed to commission device: $e', name: 'MatterBindingService');
      rethrow;
    }
  }

  /// Clear all commissioned devices from CHIP-Tool controller
  Future<Map<String, dynamic>> clearAllCommissionedDevices() async {
    try {
      developer.log('Clearing all commissioned devices from CHIP-Tool controller...', name: 'MatterBindingService');

      final Map<String, dynamic> result = await _channel.invokeMethod('clearAllCommissionedDevices');

      developer.log('Clear result: $result', name: 'MatterBindingService');
      return result;
    } catch (e) {
      developer.log('Failed to clear commissioned devices: $e', name: 'MatterBindingService');
      return {
        'success': false,
        'message': 'Error clearing devices: $e'
      };
    }
  }

  /// Discover Matter devices commissioned to CHIP-Tool controller
  Future<List<MatterDevice>> discoverMatterDevices() async {
    try {
      developer.log('Discovering Matter devices commissioned to CHIP-Tool controller...', name: 'MatterBindingService');

      final List<dynamic> result = await _channel.invokeMethod('discoverMatterDevices');

      return result.map((deviceData) => MatterDevice.fromMap(deviceData)).toList();
    } catch (e) {
      developer.log('Failed to discover Matter devices: $e', name: 'MatterBindingService');
      rethrow;
    }
  }



  /// Bind two Matter devices together using CHIP-Tool SDK
  /// This performs the complete binding workflow:
  /// 1. Check if devices are commissioned to CHIPTool
  /// 2. Set up ACL permissions
  /// 3. Create binding relationship
  /// Equivalent to the terminal commands:
  /// - chip-tool accesscontrol write acl '[...ACL...]' <target-node-id> 0
  /// - chip-tool binding write binding '[...binding...]' <source-node-id> <source-endpoint>
  ///
  /// [sourceNodeId] - The node ID of the source device (controller)
  /// [targetNodeId] - The node ID of the target device (controlled)
  /// [sourceEndpoint] - The endpoint on the source device (default: 1)
  /// [targetEndpoint] - The endpoint on the target device (default: 1)
  /// [clusterId] - The cluster ID for the binding (default: 6 for OnOff)
  Future<MatterBindingResult> bindDevices({
    required int sourceNodeId,
    required int targetNodeId,
    int sourceEndpoint = 1,
    int targetEndpoint = 1,
    int clusterId = 6, // OnOff cluster
  }) async {
    try {
      developer.log(
        'Starting Matter binding workflow: source=$sourceNodeId, target=$targetNodeId, cluster=$clusterId',
        name: 'MatterBindingService',
      );

      // First check if devices are commissioned to CHIPTool
      final commissionedDevices = await discoverMatterDevices();
      final sourceCommissioned = commissionedDevices.any((device) => device.nodeId == sourceNodeId);
      final targetCommissioned = commissionedDevices.any((device) => device.nodeId == targetNodeId);

      developer.log(
        'Device commissioning status: Source($sourceNodeId)=$sourceCommissioned, Target($targetNodeId)=$targetCommissioned',
        name: 'MatterBindingService',
      );

      // If devices are not commissioned, show helpful error message
      if (!sourceCommissioned || !targetCommissioned) {
        throw Exception(
          'Devices must be commissioned to CHIPTool before binding.\n'
          'Source device (Node $sourceNodeId) commissioned: $sourceCommissioned\n'
          'Target device (Node $targetNodeId) commissioned: $targetCommissioned\n\n'
          'Please use the commissioning screen to commission devices first.'
        );
      }

      final Map<String, dynamic> result = await _channel.invokeMethod('bindDevices', {
        'sourceNodeId': sourceNodeId,
        'targetNodeId': targetNodeId,
        'sourceEndpoint': sourceEndpoint,
        'targetEndpoint': targetEndpoint,
        'clusterId': clusterId,
      });

      return MatterBindingResult.fromMap(result);
    } catch (e) {
      developer.log('Failed to bind devices: $e', name: 'MatterBindingService');
      rethrow;
    }
  }

  /// Unbind two Matter devices
  Future<MatterBindingResult> unbindDevices({
    required int sourceNodeId,
    required int targetNodeId,
    int sourceEndpoint = 1,
    int targetEndpoint = 1,
    int clusterId = 6,
  }) async {
    try {
      developer.log(
        'Unbinding devices: source=$sourceNodeId, target=$targetNodeId, cluster=$clusterId',
        name: 'MatterBindingService',
      );

      final Map<String, dynamic> result = await _channel.invokeMethod('unbindDevices', {
        'sourceNodeId': sourceNodeId,
        'targetNodeId': targetNodeId,
        'sourceEndpoint': sourceEndpoint,
        'targetEndpoint': targetEndpoint,
        'clusterId': clusterId,
      });

      return MatterBindingResult.fromMap(result);
    } catch (e) {
      developer.log('Failed to unbind devices: $e', name: 'MatterBindingService');
      rethrow;
    }
  }

  /// Open commissioning window for device sharing
  Future<MatterCommissioningResult> openCommissioningWindow({
    required int nodeId,
    int duration = 180, // 3 minutes default
  }) async {
    try {
      developer.log(
        'Opening commissioning window for node $nodeId, duration: ${duration}s',
        name: 'MatterBindingService',
      );

      final Map<String, dynamic> result = await _channel.invokeMethod('openCommissioningWindow', {
        'nodeId': nodeId,
        'duration': duration,
      });

      return MatterCommissioningResult.fromMap(result);
    } catch (e) {
      developer.log('Failed to open commissioning window: $e', name: 'MatterBindingService');
      rethrow;
    }
  }

  /// Get current bindings for a device
  Future<List<MatterBinding>> getDeviceBindings(int nodeId) async {
    try {
      developer.log('Getting bindings for node $nodeId', name: 'MatterBindingService');

      final List<dynamic> result = await _channel.invokeMethod('getDeviceBindings', {
        'nodeId': nodeId,
      });

      return result.map((bindingData) => MatterBinding.fromMap(bindingData)).toList();
    } catch (e) {
      developer.log('Failed to get device bindings: $e', name: 'MatterBindingService');
      rethrow;
    }
  }

  /// Get cluster name from cluster ID
  static String getClusterName(int clusterId) {
    switch (clusterId) {
      case 6:
        return 'OnOff';
      case 8:
        return 'Level Control';
      case 768:
        return 'Color Control';
      case 257:
        return 'Door Lock';
      case 258:
        return 'Window Covering';
      case 513:
        return 'Thermostat';
      default:
        return 'Unknown (0x${clusterId.toRadixString(16)})';
    }
  }

  /// Get device type name from device type ID
  static String getDeviceTypeName(int deviceType) {
    switch (deviceType) {
      case 256:
        return 'On/Off Light';
      case 257:
        return 'Dimmable Light';
      case 268:
        return 'Color Temperature Light';
      case 269:
        return 'Extended Color Light';
      case 259:
        return 'On/Off Plug-in Unit';
      case 266:
        return 'On/Off Light Switch';
      case 267:
        return 'Dimmer Switch';
      default:
        return 'Unknown Device';
    }
  }
}

/// Represents a Matter device discovered on the network
class MatterDevice {
  final int nodeId;
  final String name;
  final String deviceType;
  final List<int> endpoints;
  final List<int> clusters;

  MatterDevice({
    required this.nodeId,
    required this.name,
    required this.deviceType,
    required this.endpoints,
    required this.clusters,
  });

  factory MatterDevice.fromMap(dynamic mapData) {
    // Handle both Map<String, dynamic> and Map<Object?, Object?> types
    final Map<String, dynamic> map;
    if (mapData is Map<String, dynamic>) {
      map = mapData;
    } else if (mapData is Map) {
      // Convert Map<Object?, Object?> to Map<String, dynamic>
      map = Map<String, dynamic>.from(mapData.map((key, value) => MapEntry(key.toString(), value)));
    } else {
      throw ArgumentError('Expected Map but got ${mapData.runtimeType}');
    }

    return MatterDevice(
      nodeId: (map['nodeId'] is int) ? map['nodeId'] : (map['nodeId'] as num?)?.toInt() ?? 0,
      name: map['name']?.toString() ?? 'Unknown Device',
      deviceType: map['deviceType']?.toString() ?? 'unknown',
      endpoints: (map['endpoints'] as List?)?.map((e) => (e is int) ? e : (e as num?)?.toInt() ?? 1).toList() ?? [1],
      clusters: (map['clusters'] as List?)?.map((e) => (e is int) ? e : (e as num?)?.toInt() ?? 6).toList() ?? [],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'nodeId': nodeId,
      'name': name,
      'deviceType': deviceType,
      'endpoints': endpoints,
      'clusters': clusters,
    };
  }

  @override
  String toString() {
    return 'MatterDevice(nodeId: $nodeId, name: $name, type: $deviceType)';
  }
}

/// Result of a Matter binding operation
class MatterBindingResult {
  final bool success;
  final String message;
  final int? sourceNodeId;
  final int? targetNodeId;
  final int? clusterId;

  MatterBindingResult({
    required this.success,
    required this.message,
    this.sourceNodeId,
    this.targetNodeId,
    this.clusterId,
  });

  factory MatterBindingResult.fromMap(dynamic mapData) {
    // Handle both Map<String, dynamic> and Map<Object?, Object?> types
    final Map<String, dynamic> map;
    if (mapData is Map<String, dynamic>) {
      map = mapData;
    } else if (mapData is Map) {
      // Convert Map<Object?, Object?> to Map<String, dynamic>
      map = Map<String, dynamic>.from(mapData.map((key, value) => MapEntry(key.toString(), value)));
    } else {
      throw ArgumentError('Expected Map but got ${mapData.runtimeType}');
    }

    return MatterBindingResult(
      success: map['success'] == true,
      message: map['message']?.toString() ?? '',
      sourceNodeId: (map['sourceNodeId'] is int) ? map['sourceNodeId'] : (map['sourceNodeId'] as num?)?.toInt(),
      targetNodeId: (map['targetNodeId'] is int) ? map['targetNodeId'] : (map['targetNodeId'] as num?)?.toInt(),
      clusterId: (map['clusterId'] is int) ? map['clusterId'] : (map['clusterId'] as num?)?.toInt(),
    );
  }
}

/// Result of device commissioning or commissioning window operations
class MatterCommissioningResult {
  final bool success;
  final String message;
  final int? nodeId;
  final int? duration;
  final String? setupCode;

  MatterCommissioningResult({
    required this.success,
    required this.message,
    this.nodeId,
    this.duration,
    this.setupCode,
  });

  factory MatterCommissioningResult.fromMap(dynamic mapData) {
    // Handle both Map<String, dynamic> and Map<Object?, Object?> types
    final Map<String, dynamic> map;
    if (mapData is Map<String, dynamic>) {
      map = mapData;
    } else if (mapData is Map) {
      // Convert Map<Object?, Object?> to Map<String, dynamic>
      map = Map<String, dynamic>.from(mapData.map((key, value) => MapEntry(key.toString(), value)));
    } else {
      throw ArgumentError('Expected Map but got ${mapData.runtimeType}');
    }

    return MatterCommissioningResult(
      success: map['success'] == true,
      message: map['message']?.toString() ?? '',
      nodeId: (map['nodeId'] is int) ? map['nodeId'] : (map['nodeId'] as num?)?.toInt(),
      duration: (map['duration'] is int) ? map['duration'] : (map['duration'] as num?)?.toInt(),
      setupCode: map['setupCode']?.toString(),
    );
  }

  @override
  String toString() {
    return 'MatterCommissioningResult(success: $success, message: $message, nodeId: $nodeId, duration: $duration, setupCode: $setupCode)';
  }
}

/// Represents a Matter device binding
class MatterBinding {
  final int sourceEndpoint;
  final int clusterId;
  final int targetNodeId;
  final int targetEndpoint;

  MatterBinding({
    required this.sourceEndpoint,
    required this.clusterId,
    required this.targetNodeId,
    required this.targetEndpoint,
  });

  factory MatterBinding.fromMap(dynamic mapData) {
    // Handle both Map<String, dynamic> and Map<Object?, Object?> types
    final Map<String, dynamic> map;
    if (mapData is Map<String, dynamic>) {
      map = mapData;
    } else if (mapData is Map) {
      // Convert Map<Object?, Object?> to Map<String, dynamic>
      map = Map<String, dynamic>.from(mapData.map((key, value) => MapEntry(key.toString(), value)));
    } else {
      throw ArgumentError('Expected Map but got ${mapData.runtimeType}');
    }

    return MatterBinding(
      sourceEndpoint: (map['sourceEndpoint'] is int) ? map['sourceEndpoint'] : (map['sourceEndpoint'] as num?)?.toInt() ?? 1,
      clusterId: (map['clusterId'] is int) ? map['clusterId'] : (map['clusterId'] as num?)?.toInt() ?? 6,
      targetNodeId: (map['targetNodeId'] is int) ? map['targetNodeId'] : (map['targetNodeId'] as num?)?.toInt() ?? 0,
      targetEndpoint: (map['targetEndpoint'] is int) ? map['targetEndpoint'] : (map['targetEndpoint'] as num?)?.toInt() ?? 1,
    );
  }

  String get clusterName => MatterBindingService.getClusterName(clusterId);

  @override
  String toString() {
    return 'MatterBinding(endpoint: $sourceEndpoint, cluster: $clusterName, target: $targetNodeId:$targetEndpoint)';
  }
}
